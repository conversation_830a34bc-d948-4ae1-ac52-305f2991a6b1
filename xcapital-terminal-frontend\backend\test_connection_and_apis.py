#!/usr/bin/env python
"""
Script de test complet pour vérifier la connexion DB et les APIs optimisées
"""
import os
import sys
import django
import time
import requests
import json
from django.core.management.color import make_style
from django.db import connection
from django.core.cache import cache

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

from market_data.postgres_models import XCapitalCompany, XCapitalCompanyBond

style = make_style()

def test_database_connection():
    """Test de la connexion à la base de données"""
    print(style.SUCCESS("🔍 Test de connexion à la base de données PostgreSQL"))
    print("=" * 60)
    
    try:
        # Test de connexion basique
        with connection.cursor() as cursor:
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            print(style.SUCCESS(f"✅ Connexion PostgreSQL réussie"))
            print(f"   Version: {version}")
        
        # Test des tables XCapital
        companies_count = XCapitalCompany.objects.count()
        bonds_count = XCapitalCompanyBond.objects.count()
        
        print(f"✅ Tables XCapital accessibles:")
        print(f"   Entreprises: {companies_count:,}")
        print(f"   Prix (bonds): {bonds_count:,}")
        
        # Test d'une requête optimisée
        start_time = time.time()
        sample_company = XCapitalCompany.objects.first()
        if sample_company:
            recent_bonds = sample_company.bonds.filter(
                date_trade__gte='2024-01-01'
            )[:10]
            query_time = time.time() - start_time
            
            print(f"✅ Requête test réussie en {query_time:.3f}s")
            print(f"   Entreprise test: {sample_company.symbol}")
            print(f"   Données récentes: {recent_bonds.count()} enregistrements")
            
            return sample_company.symbol
        else:
            print(style.WARNING("⚠️  Aucune entreprise trouvée dans la base"))
            return None
            
    except Exception as e:
        print(style.ERROR(f"❌ Erreur de connexion: {e}"))
        return None


def test_cache_system():
    """Test du système de cache"""
    print(f"\n{style.SUCCESS('🚀 Test du système de cache')}")
    print("=" * 60)
    
    try:
        # Test du cache par défaut
        test_key = "test_cache_key"
        test_value = {"test": "data", "timestamp": time.time()}
        
        # Écriture dans le cache
        cache.set(test_key, test_value, 60)
        print("✅ Écriture dans le cache réussie")
        
        # Lecture du cache
        cached_data = cache.get(test_key)
        if cached_data and cached_data["test"] == "data":
            print("✅ Lecture du cache réussie")
            print(f"   Données récupérées: {cached_data}")
        else:
            print(style.ERROR("❌ Échec de la lecture du cache"))
        
        # Nettoyage
        cache.delete(test_key)
        print("✅ Nettoyage du cache réussi")
        
        return True
        
    except Exception as e:
        print(style.ERROR(f"❌ Erreur du système de cache: {e}"))
        return False


def test_api_endpoints(test_symbol, base_url="http://127.0.0.1:8000"):
    """Test des endpoints API originaux et optimisés"""
    print(f"\n{style.SUCCESS('🌐 Test des APIs XCapital')}")
    print("=" * 60)

    # Test 1: Companies List API (Simplified)
    print(f"🧪 Test 1: Companies List API (Simplified)")
    try:
        start_time = time.time()
        response = requests.get(f"{base_url}/api/v1/xcapital/companies/", timeout=15)
        response_time = time.time() - start_time

        if response.status_code == 200:
            data = response.json()
            print(style.SUCCESS(f"✅ Companies List - Succès en {response_time:.3f}s"))
            if 'companies' in data:
                print(f"   Total entreprises: {data.get('total_companies', 'N/A')}")
                if data['companies']:
                    sample_company = data['companies'][0]
                    print(f"   Exemple: {sample_company.get('symbol', 'N/A')} - {sample_company.get('nom_francais', 'N/A')}")
        else:
            print(style.ERROR(f"❌ Companies List - Erreur HTTP {response.status_code}"))
            print(f"   Réponse: {response.text[:200]}...")

    except Exception as e:
        print(style.ERROR(f"❌ Companies List - Exception: {e}"))

    if not test_symbol:
        print(style.ERROR("❌ Aucun symbole de test disponible pour les autres tests"))
        return
    
    # Payload de test
    test_payload = {
        "company_symbol": test_symbol,
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "page_size": 100,
        "use_cache": True
    }
    
    # Test 2: Secure Company Data API (Original)
    print(f"\n🧪 Test 2: Secure Company Data API (Original)")
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/xcapital/secure/company-data/",
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        response_time = time.time() - start_time

        if response.status_code == 200:
            data = response.json()
            print(style.SUCCESS(f"✅ Secure Company Data - Succès en {response_time:.3f}s"))
            if 'data' in data:
                print(f"   Enregistrements retournés: {len(data['data'])}")
            if 'period_statistics' in data:
                stats = data['period_statistics']
                print(f"   Premier prix: {stats.get('first_price', 'N/A')}")
                print(f"   Dernier prix: {stats.get('last_price', 'N/A')}")
            if 'period' in data:
                period = data['period']
                print(f"   Période: {period.get('actual_start_date', 'N/A')} à {period.get('actual_end_date', 'N/A')}")
        else:
            print(style.ERROR(f"❌ Secure Company Data - Erreur HTTP {response.status_code}"))
            print(f"   Réponse: {response.text[:300]}...")

    except Exception as e:
        print(style.ERROR(f"❌ Secure Company Data - Exception: {e}"))

    # Test 2b: Secure Company Data API (GET for info)
    print(f"\n🧪 Test 2b: Secure Company Data API (GET - Info)")
    try:
        start_time = time.time()
        response = requests.get(f"{base_url}/api/v1/xcapital/secure/company-data/", timeout=15)
        response_time = time.time() - start_time

        if response.status_code == 200:
            data = response.json()
            print(style.SUCCESS(f"✅ Secure API Info - Succès en {response_time:.3f}s"))
            if 'database_stats' in data:
                stats = data['database_stats']
                print(f"   Total entreprises: {stats.get('total_companies', 'N/A')}")
                print(f"   Total prix: {stats.get('total_price_records', 'N/A')}")
        else:
            print(style.ERROR(f"❌ Secure API Info - Erreur HTTP {response.status_code}"))

    except Exception as e:
        print(style.ERROR(f"❌ Secure API Info - Exception: {e}"))

    # Test 3: API optimisée v2
    print(f"\n🚀 Test 3: API Optimisée v2: /api/v1/xcapital/v2/company-data/")
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/xcapital/v2/company-data/",
            json=test_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            print(style.SUCCESS(f"✅ API Optimisée v2 - Succès en {response_time:.3f}s"))
            
            if 'data' in data:
                print(f"   Enregistrements retournés: {len(data['data'])}")
                
            if 'performance_info' in data:
                perf_info = data['performance_info']
                print(f"   Cache utilisé: {perf_info.get('cache_used', 'N/A')}")
                print(f"   Requête optimisée: {perf_info.get('query_optimized', 'N/A')}")
                
            if 'period' in data:
                period_info = data['period']
                print(f"   Total disponible: {period_info.get('total_available_records', 'N/A')}")
                print(f"   Retournés: {period_info.get('returned_records', 'N/A')}")
                
        else:
            print(style.ERROR(f"❌ API Optimisée v2 - Erreur HTTP {response.status_code}"))
            print(f"   Réponse: {response.text[:200]}...")
            
    except Exception as e:
        print(style.ERROR(f"❌ API Optimisée v2 - Exception: {e}"))
    
    # Test de l'API market overview optimisée
    print(f"\n📊 Test Market Overview Optimisé: /api/v1/xcapital/v2/market/overview/")
    try:
        start_time = time.time()
        response = requests.get(f"{base_url}/api/v1/xcapital/v2/market/overview/", timeout=15)
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            data = response.json()
            print(style.SUCCESS(f"✅ Market Overview - Succès en {response_time:.3f}s"))
            
            if 'market_overview' in data:
                overview = data['market_overview']
                print(f"   Total entreprises: {overview.get('total_companies', 'N/A')}")
                print(f"   Total prix: {overview.get('total_price_records', 'N/A')}")
                print(f"   Cache info: {overview.get('cache_info', 'N/A')}")
        else:
            print(style.ERROR(f"❌ Market Overview - Erreur HTTP {response.status_code}"))
            
    except Exception as e:
        print(style.ERROR(f"❌ Market Overview - Exception: {e}"))


def test_performance_comparison(test_symbol, base_url="http://127.0.0.1:8000"):
    """Comparaison de performance entre API originale et optimisée"""
    print(f"\n{style.SUCCESS('⚡ Comparaison de Performance')}")
    print("=" * 60)
    
    if not test_symbol:
        print(style.ERROR("❌ Aucun symbole de test disponible"))
        return
    
    test_payload = {
        "company_symbol": test_symbol,
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "page_size": 500
    }
    
    # Test API originale (3 fois)
    original_times = []
    for i in range(3):
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/v1/xcapital/secure/company-data/",
                json=test_payload,
                timeout=30
            )
            if response.status_code == 200:
                original_times.append(time.time() - start_time)
        except:
            pass
    
    # Test API optimisée (3 fois)
    optimized_times = []
    for i in range(3):
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/v1/xcapital/v2/company-data/",
                json={**test_payload, "use_cache": True},
                timeout=30
            )
            if response.status_code == 200:
                optimized_times.append(time.time() - start_time)
        except:
            pass
    
    # Analyse des résultats
    if original_times and optimized_times:
        avg_original = sum(original_times) / len(original_times)
        avg_optimized = sum(optimized_times) / len(optimized_times)
        improvement = ((avg_original - avg_optimized) / avg_original) * 100
        
        print(f"📊 Résultats de performance:")
        print(f"   API Originale - Temps moyen: {avg_original:.3f}s")
        print(f"   API Optimisée - Temps moyen: {avg_optimized:.3f}s")
        print(style.SUCCESS(f"   🎯 Amélioration: {improvement:.1f}%"))
        
        if improvement > 50:
            print(style.SUCCESS("   🏆 Excellente amélioration de performance!"))
        elif improvement > 20:
            print(style.SUCCESS("   ✅ Bonne amélioration de performance"))
        else:
            print(style.WARNING("   ⚠️  Amélioration modérée"))
    else:
        print(style.ERROR("❌ Impossible de comparer les performances"))


def main():
    """Fonction principale de test"""
    print(style.SUCCESS("🎯 XCapital Terminal - Test Complet du Système"))
    print("=" * 80)
    
    # 1. Test de connexion DB
    test_symbol = test_database_connection()
    
    # 2. Test du cache
    cache_ok = test_cache_system()
    
    # 3. Test des APIs
    if test_symbol:
        test_api_endpoints(test_symbol)
        test_performance_comparison(test_symbol)
    
    # 4. Résumé final
    print(f"\n{style.SUCCESS('📋 Résumé des Tests')}")
    print("=" * 60)
    
    if test_symbol and cache_ok:
        print(style.SUCCESS("✅ Tous les systèmes sont opérationnels"))
        print("✅ Base de données PostgreSQL connectée")
        print("✅ Système de cache fonctionnel")
        print("✅ APIs optimisées disponibles")
        print(f"\n🚀 Votre XCapital Terminal est prêt avec les optimisations!")
        print(f"   Utilisez l'API v2: /api/v1/xcapital/v2/company-data/")
    else:
        print(style.ERROR("❌ Certains systèmes nécessitent une attention"))


if __name__ == '__main__':
    main()
