#!/usr/bin/env python
"""
Script de diagnostic pour tester la connectivité de la base de données
"""
import socket
import psycopg2
import os
import sys
from django.core.management.color import make_style

style = make_style()

def test_dns_resolution():
    """Test de résolution DNS"""
    print(style.SUCCESS("🔍 Test de résolution DNS"))
    print("=" * 60)
    
    hostname = "xcapitaldatabase.cbeceekg4vdf.eu-north-1.rds.amazonaws.com"
    port = 5432
    
    try:
        # Test de résolution DNS
        ip_address = socket.gethostbyname(hostname)
        print(style.SUCCESS(f"✅ DNS Resolution successful"))
        print(f"   Hostname: {hostname}")
        print(f"   IP Address: {ip_address}")
        
        # Test de connectivité TCP
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((hostname, port))
        sock.close()
        
        if result == 0:
            print(style.SUCCESS(f"✅ TCP Connection successful"))
            print(f"   Port {port} is open and accessible")
            return True
        else:
            print(style.ERROR(f"❌ TCP Connection failed"))
            print(f"   Port {port} is not accessible (Error code: {result})")
            return False
            
    except socket.gaierror as e:
        print(style.ERROR(f"❌ DNS Resolution failed"))
        print(f"   Error: {e}")
        print(f"   This indicates the hostname cannot be resolved")
        return False
    except Exception as e:
        print(style.ERROR(f"❌ Network test failed"))
        print(f"   Error: {e}")
        return False

def test_postgresql_connection():
    """Test de connexion PostgreSQL directe"""
    print(f"\n{style.SUCCESS('🐘 Test de connexion PostgreSQL')}")
    print("=" * 60)
    
    connection_params = {
        'host': 'xcapitaldatabase.cbeceekg4vdf.eu-north-1.rds.amazonaws.com',
        'port': '5432',
        'database': 'xcapital',
        'user': 'postgres',
        'password': 'b5,d28FK/yXMP6CuK!-Xmy3@',
        'sslmode': 'require',
        'connect_timeout': 10
    }
    
    try:
        print("Tentative de connexion PostgreSQL...")
        conn = psycopg2.connect(**connection_params)
        
        # Test d'une requête simple
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        
        cursor.execute("SELECT current_database();")
        database = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        print(style.SUCCESS("✅ PostgreSQL Connection successful"))
        print(f"   Database: {database}")
        print(f"   Version: {version}")
        return True
        
    except psycopg2.OperationalError as e:
        print(style.ERROR("❌ PostgreSQL Connection failed"))
        print(f"   Error: {e}")
        return False
    except Exception as e:
        print(style.ERROR("❌ Unexpected error"))
        print(f"   Error: {e}")
        return False

def test_alternative_hostnames():
    """Test d'hostnames alternatifs"""
    print(f"\n{style.SUCCESS('🔄 Test d hostnames alternatifs')}")
    print("=" * 60)
    
    alternative_hosts = [
        "xcapitaldatabase.cbeceekg4vdf.eu-north-1.rds.amazonaws.com",
        "xcapital-database.cbeceekg4vdf.eu-north-1.rds.amazonaws.com",
        "xcapital.cbeceekg4vdf.eu-north-1.rds.amazonaws.com"
    ]
    
    for host in alternative_hosts:
        print(f"Testing: {host}")
        try:
            ip = socket.gethostbyname(host)
            print(style.SUCCESS(f"  ✅ Resolved to: {ip}"))
        except socket.gaierror:
            print(style.ERROR(f"  ❌ Cannot resolve"))
        print()

def check_network_requirements():
    """Vérification des prérequis réseau"""
    print(f"\n{style.SUCCESS('🌐 Verification des prerequis reseau')}")
    print("=" * 60)
    
    # Test de connectivité Internet générale
    try:
        socket.gethostbyname("google.com")
        print(style.SUCCESS("✅ Internet connectivity: OK"))
    except:
        print(style.ERROR("❌ Internet connectivity: FAILED"))
        print("   Vérifiez votre connexion Internet")
    
    # Test de résolution DNS AWS
    try:
        socket.gethostbyname("aws.amazon.com")
        print(style.SUCCESS("✅ AWS DNS resolution: OK"))
    except:
        print(style.ERROR("❌ AWS DNS resolution: FAILED"))
        print("   Problème avec la résolution DNS AWS")
    
    # Test de connectivité vers d'autres services AWS
    try:
        socket.gethostbyname("rds.eu-north-1.amazonaws.com")
        print(style.SUCCESS("✅ AWS RDS region DNS: OK"))
    except:
        print(style.ERROR("❌ AWS RDS region DNS: FAILED"))
        print("   Problème avec la région eu-north-1")

def provide_solutions():
    """Fournir des solutions basées sur les tests"""
    print(f"\n{style.SUCCESS('💡 Solutions recommandees')}")
    print("=" * 60)
    
    print("1. 🔧 Vérifications immédiates:")
    print("   - Vérifiez votre connexion Internet")
    print("   - Redémarrez votre routeur/modem")
    print("   - Essayez depuis un autre réseau (mobile hotspot)")
    print()
    
    print("2. 🏢 Problèmes d'entreprise/firewall:")
    print("   - Vérifiez les paramètres de proxy d'entreprise")
    print("   - Contactez votre administrateur réseau")
    print("   - Ports requis: 5432 (PostgreSQL)")
    print()
    
    print("3. ☁️  Problèmes AWS RDS:")
    print("   - L'instance RDS pourrait être arrêtée")
    print("   - Vérifiez les groupes de sécurité AWS")
    print("   - L'hostname pourrait être incorrect")
    print()
    
    print("4. 🔄 Solutions de contournement:")
    print("   - Utiliser une base de données locale")
    print("   - Configurer SQLite pour le développement")
    print("   - Utiliser Docker avec PostgreSQL")

def main():
    """Fonction principale de diagnostic"""
    print(style.SUCCESS("🎯 XCapital Terminal - Diagnostic de Base de Données"))
    print("=" * 80)
    
    # Tests de connectivité
    dns_ok = test_dns_resolution()
    
    if dns_ok:
        db_ok = test_postgresql_connection()
    else:
        print(style.WARNING("⚠️  Skipping PostgreSQL test due to DNS failure"))
        db_ok = False
    
    # Tests supplémentaires
    test_alternative_hostnames()
    check_network_requirements()
    
    # Solutions
    provide_solutions()
    
    # Résumé
    print(f"\n{style.SUCCESS('📋 RÉSUMÉ DU DIAGNOSTIC')}")
    print("=" * 80)
    
    if dns_ok and db_ok:
        print(style.SUCCESS("✅ Base de données accessible - Le problème pourrait être dans Django"))
    elif dns_ok and not db_ok:
        print(style.WARNING("⚠️  DNS OK mais connexion PostgreSQL échoue - Problème d'authentification/configuration"))
    else:
        print(style.ERROR("❌ Problème de connectivité réseau - L'instance RDS n'est pas accessible"))
    
    return dns_ok, db_ok

if __name__ == '__main__':
    main()
