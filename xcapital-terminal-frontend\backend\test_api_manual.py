#!/usr/bin/env python
"""
Test manuel de l'API avec requête directe
"""
import os
import sys
import django
import json
from django.test import RequestFactory
from django.core.management.color import make_style

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

from market_data import postgres_views

style = make_style()

def test_api_directly():
    """Test direct de l'API sans passer par le serveur"""
    print(style.SUCCESS("🧪 Test direct de l'API secure"))
    print("=" * 60)
    
    factory = RequestFactory()
    
    # Test POST avec CIH
    print("Test POST avec CIH:")
    try:
        post_data = {
            "company_symbol": "CIH",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        }
        
        request = factory.post(
            '/api/v1/xcapital/secure/company-data/',
            data=json.dumps(post_data),
            content_type='application/json'
        )
        
        # Appel direct de la fonction
        response = postgres_views.secure_company_price_data(request)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.data
            print(style.SUCCESS("✅ Succès!"))
            
            if 'company_info' in data:
                print(f"   Company: {data['company_info']['symbol']}")
                print(f"   Name: {data['company_info']['nom_francais']}")
            
            if 'period' in data:
                print(f"   Records: {data['period']['total_records']}")
                print(f"   Period: {data['period']['actual_start_date']} to {data['period']['actual_end_date']}")
            
            if 'data' in data and data['data']:
                sample = data['data'][0]
                print(f"   Sample: {sample['date']} - {sample['close_price']}")
                
        else:
            print(style.ERROR(f"❌ Erreur {response.status_code}"))
            if hasattr(response, 'data'):
                print(f"   Message: {response.data.get('message', 'N/A')}")
                
    except Exception as e:
        print(style.ERROR(f"❌ Exception: {e}"))
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_api_directly()
