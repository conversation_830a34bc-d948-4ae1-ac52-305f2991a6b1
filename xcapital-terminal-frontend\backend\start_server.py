#!/usr/bin/env python
"""
Script simple pour démarrer le serveur Django
"""
import os
import sys
import django
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
    
    # Démarrer le serveur directement
    sys.argv = ['manage.py', 'runserver', '127.0.0.1:8000']
    execute_from_command_line(sys.argv)
