#!/usr/bin/env python
"""
Script de configuration de base de données pour XCapital Terminal
"""
import os
import sys
import django
from django.core.management import execute_from_command_line
from django.core.management.color import make_style
from django.db import connection
from decimal import Decimal
from datetime import date, datetime

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

from market_data.postgres_models import XCapitalCompany, XCapitalCompanyBond

style = make_style()

def create_sample_data():
    """Crée des données d'exemple pour tester les APIs"""
    print(style.SUCCESS("📊 Création de données d'exemple"))
    print("=" * 60)
    
    try:
        # Créer des entreprises d'exemple
        companies_data = [
            {
                'symbol': 'CIH',
                'company_id': '449',
                'nom_francais': 'CIH',
                'nom_arabe': 'بنك التجارة والصناعة',
                'nom_anglais': 'CIH Bank'
            },
            {
                'symbol': 'ATW',
                'company_id': 'ATW_001',
                'nom_francais': 'Attijariwafa Bank',
                'nom_arabe': 'البنك التجاري وفا بنك',
                'nom_anglais': 'Attijariwafa Bank'
            },
            {
                'symbol': 'ADH',
                'company_id': 'ADH_001',
                'nom_francais': 'DOUJA PROM ADDOHA',
                'nom_arabe': 'الدوحة للترقية العقارية',
                'nom_anglais': 'DOUJA PROM ADDOHA'
            }
        ]
        
        created_companies = []
        for company_data in companies_data:
            company, created = XCapitalCompany.objects.get_or_create(
                symbol=company_data['symbol'],
                defaults=company_data
            )
            created_companies.append(company)
            if created:
                print(f"✅ Created company: {company.symbol} - {company.nom_francais}")
            else:
                print(f"ℹ️  Company exists: {company.symbol} - {company.nom_francais}")
        
        # Créer des données de prix d'exemple
        print(f"\n📈 Création de données de prix d'exemple")
        
        sample_dates = [
            date(2024, 1, 2),
            date(2024, 1, 3),
            date(2024, 1, 4),
            date(2024, 6, 15),
            date(2024, 12, 30),
            date(2024, 12, 31)
        ]
        
        base_prices = {'CIH': 385.0, 'ATW': 450.0, 'ADH': 25.0}
        
        for company in created_companies:
            base_price = base_prices.get(company.symbol, 100.0)
            
            for i, trade_date in enumerate(sample_dates):
                # Variation de prix simulée
                price_variation = (i * 2.5) - 5  # Variation de -5 à +7.5
                close_price = base_price + price_variation
                
                bond_data = {
                    'company': company,
                    'date_trade': trade_date,
                    'open_price': Decimal(str(close_price - 1.0)),
                    'high_price': Decimal(str(close_price + 2.0)),
                    'low_price': Decimal(str(close_price - 2.0)),
                    'close_price': Decimal(str(close_price)),
                    'current_price': Decimal(str(close_price)),
                    'volume': 1000 + (i * 250),
                    'shares_traded': 500 + (i * 100),
                    'total_trades': 50 + (i * 10),
                    'market_cap': Decimal(str(close_price * 1000000))
                }
                
                bond, created = XCapitalCompanyBond.objects.get_or_create(
                    company=company,
                    date_trade=trade_date,
                    defaults=bond_data
                )
                
                if created:
                    print(f"  ✅ Created price data: {company.symbol} - {trade_date} - {close_price}")
        
        print(style.SUCCESS(f"\n✅ Données d'exemple créées avec succès"))
        print(f"   Entreprises: {len(created_companies)}")
        print(f"   Prix par entreprise: {len(sample_dates)}")
        
        return True
        
    except Exception as e:
        print(style.ERROR(f"❌ Erreur lors de la création des données: {e}"))
        return False

def test_database_connection():
    """Test la connexion à la base de données"""
    print(style.SUCCESS("🔍 Test de connexion à la base de données"))
    print("=" * 60)
    
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
        print(style.SUCCESS("✅ Connexion à la base de données réussie"))
        
        # Afficher les informations de la base
        db_settings = connection.settings_dict
        engine = db_settings['ENGINE']
        
        if 'sqlite' in engine:
            print(f"   Type: SQLite")
            print(f"   Fichier: {db_settings['NAME']}")
        elif 'postgresql' in engine:
            print(f"   Type: PostgreSQL")
            print(f"   Host: {db_settings['HOST']}")
            print(f"   Database: {db_settings['NAME']}")
        
        return True
        
    except Exception as e:
        print(style.ERROR(f"❌ Erreur de connexion: {e}"))
        return False

def run_migrations():
    """Exécute les migrations Django"""
    print(style.SUCCESS("🔄 Exécution des migrations"))
    print("=" * 60)
    
    try:
        # Créer les migrations si nécessaire
        print("Création des migrations...")
        execute_from_command_line(['manage.py', 'makemigrations'])
        
        # Appliquer les migrations
        print("Application des migrations...")
        execute_from_command_line(['manage.py', 'migrate'])
        
        print(style.SUCCESS("✅ Migrations appliquées avec succès"))
        return True
        
    except Exception as e:
        print(style.ERROR(f"❌ Erreur lors des migrations: {e}"))
        return False

def main():
    """Fonction principale de configuration"""
    print(style.SUCCESS("🎯 XCapital Terminal - Configuration de Base de Données"))
    print("=" * 80)
    
    # 1. Test de connexion
    if not test_database_connection():
        print(style.ERROR("❌ Impossible de se connecter à la base de données"))
        return 1
    
    # 2. Migrations
    if not run_migrations():
        print(style.ERROR("❌ Échec des migrations"))
        return 1
    
    # 3. Création de données d'exemple
    if not create_sample_data():
        print(style.ERROR("❌ Échec de la création des données d'exemple"))
        return 1
    
    # 4. Résumé
    print(f"\n{style.SUCCESS('🎉 Configuration terminée avec succès!')}")
    print("=" * 80)
    print("✅ Base de données configurée")
    print("✅ Migrations appliquées")
    print("✅ Données d'exemple créées")
    print()
    print("🚀 Vous pouvez maintenant démarrer le serveur Django:")
    print("   python manage.py runserver")
    print()
    print("🧪 Testez les APIs:")
    print("   GET  http://127.0.0.1:8000/api/v1/xcapital/companies/")
    print("   POST http://127.0.0.1:8000/api/v1/xcapital/secure/company-data/")
    print("        {\"company_symbol\": \"CIH\", \"start_date\": \"2024-01-01\", \"end_date\": \"2024-12-31\"}")
    
    return 0

if __name__ == '__main__':
    exit(main())
