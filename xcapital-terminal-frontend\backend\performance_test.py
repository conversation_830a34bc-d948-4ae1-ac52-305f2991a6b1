#!/usr/bin/env python
"""
Script de test de performance pour les APIs XCapital optimisées
Compare les performances avant/après optimisation
"""
import os
import sys
import django
import time
import requests
import json
from datetime import datetime, timedelta
from statistics import mean, median
import concurrent.futures
from django.core.management.color import make_style

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

from market_data.postgres_models import XCapitalCompany

style = make_style()

class PerformanceTester:
    def __init__(self, base_url='http://127.0.0.1:8000'):
        self.base_url = base_url
        self.results = {}
        
    def test_api_endpoint(self, endpoint, payload, test_name, iterations=5):
        """Teste un endpoint API et mesure les performances"""
        print(f"🧪 Test: {test_name}")
        
        response_times = []
        success_count = 0
        
        for i in range(iterations):
            try:
                start_time = time.time()
                
                if payload:
                    response = requests.post(
                        f"{self.base_url}{endpoint}",
                        json=payload,
                        headers={'Content-Type': 'application/json'},
                        timeout=30
                    )
                else:
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=30)
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response.status_code == 200:
                    success_count += 1
                    response_times.append(response_time)
                    print(f"  ✅ Iteration {i+1}: {response_time:.3f}s")
                else:
                    print(f"  ❌ Iteration {i+1}: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"  ❌ Iteration {i+1}: Error - {e}")
        
        if response_times:
            avg_time = mean(response_times)
            median_time = median(response_times)
            min_time = min(response_times)
            max_time = max(response_times)
            
            self.results[test_name] = {
                'avg_time': avg_time,
                'median_time': median_time,
                'min_time': min_time,
                'max_time': max_time,
                'success_rate': success_count / iterations * 100,
                'iterations': iterations
            }
            
            print(f"  📊 Résultats:")
            print(f"    Temps moyen: {avg_time:.3f}s")
            print(f"    Temps médian: {median_time:.3f}s")
            print(f"    Min/Max: {min_time:.3f}s / {max_time:.3f}s")
            print(f"    Taux de succès: {success_count}/{iterations} ({success_count/iterations*100:.1f}%)")
        else:
            print(f"  ❌ Aucune réponse réussie")
            
        print()
    
    def test_concurrent_requests(self, endpoint, payload, test_name, concurrent_users=5, requests_per_user=3):
        """Teste la performance sous charge concurrente"""
        print(f"🚀 Test de charge: {test_name}")
        print(f"   Utilisateurs simultanés: {concurrent_users}")
        print(f"   Requêtes par utilisateur: {requests_per_user}")
        
        def make_request():
            try:
                start_time = time.time()
                if payload:
                    response = requests.post(
                        f"{self.base_url}{endpoint}",
                        json=payload,
                        headers={'Content-Type': 'application/json'},
                        timeout=30
                    )
                else:
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=30)
                end_time = time.time()
                
                return {
                    'success': response.status_code == 200,
                    'time': end_time - start_time,
                    'status_code': response.status_code
                }
            except Exception as e:
                return {
                    'success': False,
                    'time': 0,
                    'error': str(e)
                }
        
        # Exécution concurrente
        all_requests = []
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = []
            for user in range(concurrent_users):
                for req in range(requests_per_user):
                    futures.append(executor.submit(make_request))
            
            for future in concurrent.futures.as_completed(futures):
                all_requests.append(future.result())
        
        total_time = time.time() - start_time
        
        # Analyse des résultats
        successful_requests = [r for r in all_requests if r['success']]
        failed_requests = [r for r in all_requests if not r['success']]
        
        if successful_requests:
            response_times = [r['time'] for r in successful_requests]
            avg_time = mean(response_times)
            median_time = median(response_times)
            
            print(f"  📊 Résultats de charge:")
            print(f"    Total des requêtes: {len(all_requests)}")
            print(f"    Requêtes réussies: {len(successful_requests)}")
            print(f"    Requêtes échouées: {len(failed_requests)}")
            print(f"    Temps total: {total_time:.3f}s")
            print(f"    Temps moyen par requête: {avg_time:.3f}s")
            print(f"    Temps médian: {median_time:.3f}s")
            print(f"    Débit: {len(successful_requests)/total_time:.2f} req/s")
        
        print()
    
    def run_comprehensive_tests(self):
        """Exécute une suite complète de tests de performance"""
        print(style.SUCCESS("🎯 XCapital Terminal - Tests de Performance"))
        print("=" * 60)
        
        # Récupérer un symbole d'entreprise pour les tests
        try:
            sample_company = XCapitalCompany.objects.first()
            if not sample_company:
                print(style.ERROR("❌ Aucune entreprise trouvée dans la base de données"))
                return
            
            test_symbol = sample_company.symbol
            print(f"📊 Utilisation du symbole de test: {test_symbol}")
            print()
            
        except Exception as e:
            print(style.ERROR(f"❌ Erreur lors de la récupération des données de test: {e}"))
            return
        
        # Payload de test pour les APIs POST
        test_payload = {
            "company_symbol": test_symbol,
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "page_size": 1000,
            "use_cache": True
        }
        
        # Tests des APIs originales
        print(style.SUCCESS("🔍 Tests des APIs originales:"))
        
        self.test_api_endpoint(
            "/api/v1/xcapital/secure/company-data/",
            test_payload,
            "API Originale - Données d'entreprise"
        )
        
        self.test_api_endpoint(
            "/api/v1/xcapital/market/overview/",
            None,
            "API Originale - Vue d'ensemble du marché"
        )
        
        # Tests des APIs optimisées
        print(style.SUCCESS("⚡ Tests des APIs optimisées:"))
        
        self.test_api_endpoint(
            "/api/v1/xcapital/v2/company-data/",
            test_payload,
            "API Optimisée - Données d'entreprise"
        )
        
        self.test_api_endpoint(
            "/api/v1/xcapital/v2/market/overview/",
            None,
            "API Optimisée - Vue d'ensemble du marché"
        )
        
        # Tests de charge
        print(style.SUCCESS("🚀 Tests de charge:"))
        
        self.test_concurrent_requests(
            "/api/v1/xcapital/v2/company-data/",
            test_payload,
            "API Optimisée - Test de charge",
            concurrent_users=3,
            requests_per_user=2
        )
        
        # Résumé des performances
        self.print_performance_summary()
    
    def print_performance_summary(self):
        """Affiche un résumé des performances"""
        print(style.SUCCESS("📈 Résumé des Performances"))
        print("=" * 60)
        
        if not self.results:
            print("Aucun résultat de test disponible")
            return
        
        for test_name, results in self.results.items():
            print(f"🧪 {test_name}:")
            print(f"   Temps moyen: {results['avg_time']:.3f}s")
            print(f"   Taux de succès: {results['success_rate']:.1f}%")
            
            # Classification des performances
            if results['avg_time'] < 0.5:
                performance_rating = style.SUCCESS("Excellent")
            elif results['avg_time'] < 1.0:
                performance_rating = style.SUCCESS("Bon")
            elif results['avg_time'] < 2.0:
                performance_rating = "Acceptable"
            else:
                performance_rating = style.ERROR("À améliorer")
            
            print(f"   Performance: {performance_rating}")
            print()
        
        # Comparaison si on a les deux versions
        original_api = None
        optimized_api = None
        
        for test_name, results in self.results.items():
            if "Originale" in test_name and "Données d'entreprise" in test_name:
                original_api = results
            elif "Optimisée" in test_name and "Données d'entreprise" in test_name:
                optimized_api = results
        
        if original_api and optimized_api:
            improvement = ((original_api['avg_time'] - optimized_api['avg_time']) / original_api['avg_time']) * 100
            print(style.SUCCESS(f"🎯 Amélioration de performance: {improvement:.1f}%"))
            print(f"   API Originale: {original_api['avg_time']:.3f}s")
            print(f"   API Optimisée: {optimized_api['avg_time']:.3f}s")


def main():
    """Fonction principale"""
    tester = PerformanceTester()
    
    try:
        tester.run_comprehensive_tests()
        print(style.SUCCESS("✅ Tests de performance terminés"))
        
    except KeyboardInterrupt:
        print(style.WARNING("\n⚠️  Tests interrompus par l'utilisateur"))
    except Exception as e:
        print(style.ERROR(f"❌ Erreur lors des tests: {e}"))
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
