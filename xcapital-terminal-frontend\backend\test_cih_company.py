#!/usr/bin/env python
"""
Script de test pour vérifier les données de l'entreprise CIH
"""
import os
import sys
import django
from django.core.management.color import make_style

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

from market_data.postgres_models import XCapitalCompany, XCapitalCompanyBond

style = make_style()

def test_cih_company():
    """Test des données de l'entreprise CIH"""
    print(style.SUCCESS("🔍 Test des données de l'entreprise CIH"))
    print("=" * 60)
    
    # Vérifier si CIH existe
    try:
        cih_company = XCapitalCompany.objects.get(symbol='CIH')
        print(style.SUCCESS("✅ Entreprise CIH trouvée"))
        print(f"   Symbol: {cih_company.symbol}")
        print(f"   Nom français: {cih_company.nom_francais}")
        print(f"   Company ID: {cih_company.company_id}")
        
        # Vérifier les données de prix
        bonds_count = XCapitalCompanyBond.objects.filter(company=cih_company).count()
        print(f"   Total prix disponibles: {bonds_count}")
        
        if bonds_count > 0:
            # Récupérer quelques exemples
            recent_bonds = XCapitalCompanyBond.objects.filter(
                company=cih_company
            ).order_by('-date_trade')[:5]
            
            print(f"   Données récentes:")
            for bond in recent_bonds:
                print(f"     {bond.date_trade}: {bond.close_price}")
                
            # Test de requête pour 2024
            bonds_2024 = XCapitalCompanyBond.objects.filter(
                company=cih_company,
                date_trade__gte='2024-01-01',
                date_trade__lte='2024-12-31'
            )
            print(f"   Données 2024: {bonds_2024.count()} enregistrements")
            
            if bonds_2024.exists():
                first_2024 = bonds_2024.order_by('date_trade').first()
                last_2024 = bonds_2024.order_by('-date_trade').first()
                print(f"   Première date 2024: {first_2024.date_trade}")
                print(f"   Dernière date 2024: {last_2024.date_trade}")
        else:
            print(style.WARNING("⚠️  Aucune donnée de prix trouvée pour CIH"))
            
    except XCapitalCompany.DoesNotExist:
        print(style.ERROR("❌ Entreprise CIH non trouvée"))
        
        # Lister les entreprises disponibles
        available_companies = XCapitalCompany.objects.values_list('symbol', flat=True)[:20]
        print(f"   Entreprises disponibles (échantillon): {list(available_companies)}")
        
    except Exception as e:
        print(style.ERROR(f"❌ Erreur: {e}"))

def test_all_companies():
    """Test de toutes les entreprises"""
    print(f"\n{style.SUCCESS('📊 Test de toutes les entreprises')}")
    print("=" * 60)
    
    try:
        companies = XCapitalCompany.objects.all()
        print(f"Total entreprises: {companies.count()}")
        
        # Lister toutes les entreprises
        for company in companies:
            bonds_count = XCapitalCompanyBond.objects.filter(company=company).count()
            print(f"  {company.symbol}: {company.nom_francais} ({bonds_count} prix)")
            
    except Exception as e:
        print(style.ERROR(f"❌ Erreur: {e}"))

def main():
    """Fonction principale"""
    print(style.SUCCESS("🎯 XCapital Terminal - Test Entreprise CIH"))
    print("=" * 80)
    
    test_cih_company()
    test_all_companies()

if __name__ == '__main__':
    main()
