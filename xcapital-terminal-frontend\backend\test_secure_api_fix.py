#!/usr/bin/env python
"""
Script de test pour vérifier la correction de l'API secure
"""
import os
import sys
import django
import requests
import json
import time
from django.core.management.color import make_style

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

from market_data.postgres_models import XCapitalCompany

style = make_style()

def test_secure_api_fix():
    """Test de l'API secure après correction"""
    print(style.SUCCESS("🔧 Test de l'API Secure après correction"))
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    # Récupérer un symbole de test
    try:
        test_company = XCapitalCompany.objects.first()
        if not test_company:
            print(style.ERROR("❌ Aucune entreprise trouvée dans la base"))
            return
        
        test_symbol = test_company.symbol
        print(f"📊 Utilisation du symbole de test: {test_symbol}")
        
    except Exception as e:
        print(style.ERROR(f"❌ Erreur lors de la récupération du symbole de test: {e}"))
        return
    
    # Test 1: GET request (should return API info)
    print(f"\n🧪 Test 1: GET Request")
    try:
        start_time = time.time()
        response = requests.get(f"{base_url}/api/v1/xcapital/secure/company-data/", timeout=15)
        response_time = time.time() - start_time
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Time: {response_time:.3f}s")
        
        if response.status_code == 200:
            data = response.json()
            if 'api_info' in data:
                print(style.SUCCESS("✅ GET request successful - API info returned"))
                print(f"   API Name: {data['api_info'].get('name', 'N/A')}")
            else:
                print(style.ERROR("❌ GET request - Missing api_info"))
                print(f"   Response keys: {list(data.keys())}")
        else:
            print(style.ERROR(f"❌ GET request failed"))
            print(f"   Response: {response.text[:200]}...")
            
    except Exception as e:
        print(style.ERROR(f"❌ GET request exception: {e}"))
    
    # Test 2: POST request with CIH symbol
    print(f"\n🧪 Test 2: POST Request with CIH")
    try:
        payload = {
            "company_symbol": "CIH",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        }
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/xcapital/secure/company-data/",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        response_time = time.time() - start_time
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Time: {response_time:.3f}s")
        
        if response.status_code == 200:
            data = response.json()
            print(style.SUCCESS("✅ POST request successful"))
            
            # Check response structure
            if 'company_info' in data:
                company_info = data['company_info']
                print(f"   Company Symbol: {company_info.get('symbol', 'N/A')}")
                print(f"   Company Name: {company_info.get('nom_francais', 'N/A')}")
            else:
                print(style.ERROR("❌ Missing company_info in response"))
            
            if 'period' in data:
                period = data['period']
                print(f"   Period: {period.get('actual_start_date', 'N/A')} to {period.get('actual_end_date', 'N/A')}")
                print(f"   Total Records: {period.get('total_records', 'N/A')}")
            else:
                print(style.ERROR("❌ Missing period in response"))
            
            if 'data' in data:
                print(f"   Data Records: {len(data['data'])}")
                if data['data']:
                    sample_record = data['data'][0]
                    print(f"   Sample Record Date: {sample_record.get('date', 'N/A')}")
                    print(f"   Sample Close Price: {sample_record.get('close_price', 'N/A')}")
            else:
                print(style.ERROR("❌ Missing data array in response"))
            
            # Check if it's NOT market overview data
            if 'market_overview' in data:
                print(style.ERROR("❌ WRONG: Response contains market_overview (should be company data)"))
            else:
                print(style.SUCCESS("✅ CORRECT: Response does not contain market_overview"))
                
        elif response.status_code == 404:
            data = response.json()
            print(style.WARNING("⚠️  Company CIH not found"))
            if 'sample_companies' in data:
                print(f"   Available companies: {data['sample_companies'][:5]}")
        else:
            print(style.ERROR(f"❌ POST request failed"))
            print(f"   Response: {response.text[:300]}...")
            
    except Exception as e:
        print(style.ERROR(f"❌ POST request exception: {e}"))
    
    # Test 3: POST request with test symbol
    print(f"\n🧪 Test 3: POST Request with {test_symbol}")
    try:
        payload = {
            "company_symbol": test_symbol,
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        }
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/xcapital/secure/company-data/",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        response_time = time.time() - start_time
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Time: {response_time:.3f}s")
        
        if response.status_code == 200:
            data = response.json()
            print(style.SUCCESS("✅ POST request successful"))
            
            if 'company_info' in data and 'period' in data and 'data' in data:
                print(style.SUCCESS("✅ Response has correct structure"))
                print(f"   Company: {data['company_info'].get('symbol', 'N/A')}")
                print(f"   Records: {len(data['data'])}")
                
                if 'market_overview' not in data:
                    print(style.SUCCESS("✅ CORRECT: No market_overview in response"))
                else:
                    print(style.ERROR("❌ WRONG: market_overview found in response"))
            else:
                print(style.ERROR("❌ Response missing required fields"))
                print(f"   Response keys: {list(data.keys())}")
                
        else:
            print(style.ERROR(f"❌ POST request failed"))
            print(f"   Response: {response.text[:300]}...")
            
    except Exception as e:
        print(style.ERROR(f"❌ POST request exception: {e}"))
    
    # Test 4: Compare with market overview API
    print(f"\n🧪 Test 4: Market Overview API (for comparison)")
    try:
        start_time = time.time()
        response = requests.get(f"{base_url}/api/v1/xcapital/market/overview/", timeout=15)
        response_time = time.time() - start_time
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Time: {response_time:.3f}s")
        
        if response.status_code == 200:
            data = response.json()
            if 'market_overview' in data:
                print(style.SUCCESS("✅ Market Overview API working correctly"))
                overview = data['market_overview']
                print(f"   Total Companies: {overview.get('total_companies', 'N/A')}")
                print(f"   Total Records: {overview.get('total_price_records', 'N/A')}")
            else:
                print(style.ERROR("❌ Market Overview API missing market_overview"))
        else:
            print(style.ERROR(f"❌ Market Overview API failed"))
            
    except Exception as e:
        print(style.ERROR(f"❌ Market Overview API exception: {e}"))

def main():
    """Fonction principale"""
    print(style.SUCCESS("🎯 XCapital Terminal - Test de Correction API Secure"))
    print("=" * 80)
    
    test_secure_api_fix()
    
    print(f"\n{style.SUCCESS('📋 Résumé')}")
    print("=" * 60)
    print("Si tous les tests passent:")
    print("✅ L'API secure/company-data/ retourne les bonnes données d'entreprise")
    print("✅ L'API ne retourne plus les données market_overview par erreur")
    print("✅ La structure de réponse est correcte avec company_info, period, data")

if __name__ == '__main__':
    main()
