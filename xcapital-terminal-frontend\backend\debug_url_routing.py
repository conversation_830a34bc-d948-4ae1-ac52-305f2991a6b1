#!/usr/bin/env python
"""
Script de debug pour tester le routage des URLs
"""
import os
import sys
import django
from django.core.management.color import make_style
from django.urls import reverse, resolve
from django.test import RequestFactory
import json

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'xcapital_backend.settings')
django.setup()

from market_data import postgres_views
from django.http import JsonResponse

style = make_style()

def test_url_routing():
    """Test du routage des URLs"""
    print(style.SUCCESS("🔍 Debug du routage des URLs"))
    print("=" * 60)
    
    # Test des URLs
    test_urls = [
        '/api/v1/xcapital/secure/company-data/',
        '/api/v1/xcapital/v2/company-data/',
        '/api/v1/xcapital/market/overview/',
        '/api/v1/xcapital/v2/market/overview/',
        '/api/v1/xcapital/companies/',
    ]
    
    for url in test_urls:
        try:
            resolved = resolve(url)
            print(f"✅ URL: {url}")
            print(f"   View: {resolved.func.__name__ if hasattr(resolved.func, '__name__') else resolved.func}")
            print(f"   Module: {resolved.func.__module__ if hasattr(resolved.func, '__module__') else 'N/A'}")
            print(f"   Args: {resolved.args}")
            print(f"   Kwargs: {resolved.kwargs}")
            print()
        except Exception as e:
            print(style.ERROR(f"❌ URL: {url} - Erreur: {e}"))
            print()

def test_secure_api_directly():
    """Test direct de l'API secure"""
    print(style.SUCCESS("🧪 Test direct de l'API secure"))
    print("=" * 60)
    
    factory = RequestFactory()
    
    # Test GET
    print("Test GET:")
    try:
        request = factory.get('/api/v1/xcapital/secure/company-data/')
        response = postgres_views.secure_company_price_data(request)
        print(f"Status: {response.status_code}")
        if hasattr(response, 'data'):
            print(f"Response type: {type(response.data)}")
            if 'api_info' in response.data:
                print("✅ GET response contains api_info (correct)")
            else:
                print("❌ GET response missing api_info")
        print()
    except Exception as e:
        print(style.ERROR(f"❌ GET Error: {e}"))
        print()
    
    # Test POST
    print("Test POST:")
    try:
        post_data = {
            "company_symbol": "CIH",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        }
        request = factory.post(
            '/api/v1/xcapital/secure/company-data/',
            data=json.dumps(post_data),
            content_type='application/json'
        )
        response = postgres_views.secure_company_price_data(request)
        print(f"Status: {response.status_code}")
        if hasattr(response, 'data'):
            print(f"Response type: {type(response.data)}")
            if 'company_info' in response.data:
                print("✅ POST response contains company_info (correct)")
            elif 'market_overview' in response.data:
                print("❌ POST response contains market_overview (WRONG!)")
            else:
                print(f"❌ POST response structure: {list(response.data.keys()) if isinstance(response.data, dict) else 'Not a dict'}")
        print()
    except Exception as e:
        print(style.ERROR(f"❌ POST Error: {e}"))
        import traceback
        traceback.print_exc()
        print()

def test_market_overview_api():
    """Test de l'API market overview pour comparaison"""
    print(style.SUCCESS("🔍 Test de l'API market overview"))
    print("=" * 60)
    
    factory = RequestFactory()
    
    try:
        request = factory.get('/api/v1/xcapital/market/overview/')
        response = postgres_views.get_xcapital_market_overview(request)
        print(f"Market Overview Status: {response.status_code}")
        if hasattr(response, 'data'):
            if 'market_overview' in response.data:
                print("✅ Market overview response contains market_overview (correct)")
            else:
                print("❌ Market overview response missing market_overview")
        print()
    except Exception as e:
        print(style.ERROR(f"❌ Market Overview Error: {e}"))
        print()

def main():
    """Fonction principale"""
    print(style.SUCCESS("🎯 XCapital Terminal - Debug URL Routing"))
    print("=" * 80)
    
    # 1. Test du routage des URLs
    test_url_routing()
    
    # 2. Test direct de l'API secure
    test_secure_api_directly()
    
    # 3. Test de l'API market overview
    test_market_overview_api()
    
    print(style.SUCCESS("✅ Debug terminé"))

if __name__ == '__main__':
    main()
