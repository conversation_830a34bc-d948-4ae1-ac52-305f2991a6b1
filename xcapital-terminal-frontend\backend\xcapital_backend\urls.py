"""
URL configuration for xcapital_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.http import JsonResponse
from market_data.views import api_v1_root

def api_root(request):
    """Root API endpoint"""
    return JsonResponse({
        'message': 'Welcome to XCapital Terminal API',
        'version': '1.0.0',
        'endpoints': {
            'market_data': '/api/v1/market/',
            'predictions': '/api/v1/predictions/',
            'casablanca_data': '/api/v1/csv-data/',
            'xcapital_postgresql': '/api/v1/xcapital/',
            'admin': '/admin/',
        },
        'casablanca_stock_exchange': {
            'instruments': '/api/v1/csv-data/instruments/',
            'masi_indices': '/api/v1/csv-data/indices/',
            'market_overview': '/api/v1/csv-data/market/overview/',
            'sectors': '/api/v1/csv-data/sectors/',
            'symbols': '/api/v1/csv-data/symbols/'
        },
        'xcapital_database': {
            'companies': '/api/v1/xcapital/companies/',
            'company_prices': '/api/v1/xcapital/companies/{symbol}/prices/',
            'company_variations': '/api/v1/xcapital/companies/{symbol}/variations/',
            'form_data': '/api/v1/xcapital/companies/form-data/',
            'market_overview': '/api/v1/xcapital/market/overview/',
            'symbols': '/api/v1/xcapital/symbols/',
            'secure_data': '/api/v1/xcapital/secure/company-data/'
        },
        'xcapital_optimized_v2': {
            'optimized_company_data': '/api/v1/xcapital/v2/company-data/',
            'optimized_market_overview': '/api/v1/xcapital/v2/market/overview/',
            'cache_management': '/api/v1/xcapital/v2/admin/clear-cache/',
            'performance_info': 'Optimized APIs with caching, pagination, and query optimization'
        }
    })

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', api_root, name='api-root'),
    path('api/v1/market/', include('market_data.urls')),
    path('api/v1/predictions/', include('predictions.urls')),
    path('api/v1/csv-data/', include('market_data.csv_urls')),  # APIs CSV
    path('api/v1/xcapital/', include('market_data.postgres_urls')),  # APIs PostgreSQL
    path('api/v1/xcapital/', include('market_data.optimized_urls')),  # APIs PostgreSQL Optimisées
    path('api/v1/simple/', include('market_data.simple_urls')),  # APIs simples pour test
    path('api/v1/', api_v1_root, name='api-v1-root'),
]
