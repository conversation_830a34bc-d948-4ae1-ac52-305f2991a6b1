"""
Modèles Django pour les tables PostgreSQL XCapital
"""
from django.db import models
from django.utils import timezone
from decimal import Decimal


class XCapitalCompany(models.Model):
    """
    Modèle pour la table XCapitalTerminal_Companies
    """
    symbol = models.CharField(max_length=50, unique=True, db_index=True)
    company_id = models.CharField(max_length=100, unique=True)
    nom_francais = models.CharField(max_length=255)
    nom_arabe = models.CharField(max_length=255, null=True, blank=True)
    nom_anglais = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'XCapitalTerminal_Companies'
        ordering = ['symbol']
        verbose_name = 'XCapital Company'
        verbose_name_plural = 'XCapital Companies'
        managed = True  # Allow Django to manage table for SQLite
    
    def __str__(self):
        return f"{self.symbol} - {self.nom_francais}"
    
    @property
    def display_name(self):
        """Nom d'affichage préféré"""
        return self.nom_francais or self.nom_anglais or self.symbol


class XCapitalCompanyBond(models.Model):
    """
    Modèle pour la table XCapitalTerminal_CompanyBonds (données de prix)
    """
    company = models.ForeignKey(
        XCapitalCompany, 
        on_delete=models.CASCADE, 
        related_name='bonds',
        db_column='company_id',
        to_field='company_id'
    )
    date_trade = models.DateField(db_index=True)
    open_price = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    high_price = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    low_price = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    close_price = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    current_price = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    volume = models.BigIntegerField(null=True, blank=True, default=0)
    shares_traded = models.BigIntegerField(null=True, blank=True, default=0)
    total_trades = models.IntegerField(null=True, blank=True, default=0)
    market_cap = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    adjusted_close = models.DecimalField(max_digits=15, decimal_places=4, null=True, blank=True)
    consolidated_ratio = models.DecimalField(max_digits=10, decimal_places=6, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'XCapitalTerminal_CompanyBonds'
        ordering = ['-date_trade']
        unique_together = ['company', 'date_trade']
        verbose_name = 'XCapital Company Bond'
        verbose_name_plural = 'XCapital Company Bonds'
        managed = True  # Allow Django to manage table for SQLite
        indexes = [
            # Index composé optimisé pour les requêtes de plage de dates
            models.Index(fields=['company', 'date_trade'], name='idx_company_date'),
            models.Index(fields=['company', '-date_trade'], name='idx_company_date_desc'),
            models.Index(fields=['date_trade'], name='idx_date_trade'),
            models.Index(fields=['date_trade', 'close_price'], name='idx_date_close'),
            # Index pour les agrégations de volume
            models.Index(fields=['company', 'date_trade', 'volume'], name='idx_company_date_volume'),
            # Index pour les requêtes de prix récents
            models.Index(fields=['date_trade', 'company', 'close_price'], name='idx_recent_prices'),
        ]
    
    def __str__(self):
        return f"{self.company.symbol} - {self.date_trade} - {self.close_price}"
    
    @property
    def daily_change(self):
        """Calcul de la variation journalière (propriété calculée)"""
        try:
            previous_bond = XCapitalCompanyBond.objects.filter(
                company=self.company,
                date_trade__lt=self.date_trade
            ).order_by('-date_trade').first()

            if previous_bond and previous_bond.close_price and self.close_price:
                return self.close_price - previous_bond.close_price
            return Decimal('0.0000')
        except:
            return Decimal('0.0000')

    @property
    def daily_change_pct(self):
        """Calcul du pourcentage de variation journalière (propriété calculée)"""
        try:
            previous_bond = XCapitalCompanyBond.objects.filter(
                company=self.company,
                date_trade__lt=self.date_trade
            ).order_by('-date_trade').first()

            if previous_bond and previous_bond.close_price and self.close_price:
                change = self.close_price - previous_bond.close_price
                return (change / previous_bond.close_price) * 100
            return Decimal('0.00')
        except:
            return Decimal('0.00')
    
    def total_change_from_date(self, start_date):
        """Calcul de la variation totale depuis une date donnée"""
        try:
            start_bond = XCapitalCompanyBond.objects.filter(
                company=self.company,
                date_trade__gte=start_date
            ).order_by('date_trade').first()
            
            if start_bond and start_bond.close_price and self.close_price:
                change = self.close_price - start_bond.close_price
                change_pct = (change / start_bond.close_price) * 100
                return {
                    'absolute': change,
                    'percentage': change_pct,
                    'start_price': start_bond.close_price
                }
            return {
                'absolute': Decimal('0.0000'),
                'percentage': Decimal('0.00'),
                'start_price': self.close_price
            }
        except:
            return {
                'absolute': Decimal('0.0000'),
                'percentage': Decimal('0.00'),
                'start_price': self.close_price or Decimal('0.0000')
            }
