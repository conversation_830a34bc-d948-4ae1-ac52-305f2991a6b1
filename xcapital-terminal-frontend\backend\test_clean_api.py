#!/usr/bin/env python
"""
Test de la nouvelle API clean
"""
import requests
import json
import time
from django.core.management.color import make_style

style = make_style()

def test_clean_api():
    """Test de l'API clean"""
    print(style.SUCCESS("🧪 Test de l'API Clean"))
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test 1: GET request
    print("Test 1: GET Request")
    try:
        response = requests.get(f"{base_url}/api/v1/xcapital/clean/company-data/", timeout=15)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(style.SUCCESS("✅ GET successful"))
            print(f"   API Name: {data['api_info']['name']}")
            print(f"   Total Companies: {data['database_stats']['total_companies']}")
        else:
            print(style.ERROR(f"❌ GET failed: {response.text[:200]}"))
    except Exception as e:
        print(style.ERROR(f"❌ GET exception: {e}"))
    
    # Test 2: POST with CIH
    print(f"\nTest 2: POST with CIH")
    try:
        payload = {
            "company_symbol": "CIH",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        }
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/xcapital/clean/company-data/",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        response_time = time.time() - start_time
        
        print(f"Status: {response.status_code}")
        print(f"Response Time: {response_time:.3f}s")
        
        if response.status_code == 200:
            data = response.json()
            print(style.SUCCESS("✅ POST successful"))
            
            if 'company_info' in data:
                company = data['company_info']
                print(f"   Company: {company['symbol']} - {company['nom_francais']}")
            
            if 'period' in data:
                period = data['period']
                print(f"   Records: {period['total_records']}")
                print(f"   Period: {period['actual_start_date']} to {period['actual_end_date']}")
            
            if 'period_statistics' in data:
                stats = data['period_statistics']
                print(f"   First Price: {stats['first_price']}")
                print(f"   Last Price: {stats['last_price']}")
                print(f"   Change: {stats['period_change']} ({stats['period_change_pct']}%)")
            
            if 'data' in data and data['data']:
                print(f"   Sample Record: {data['data'][0]['date']} - {data['data'][0]['close_price']}")
                
            # Vérifier qu'il n'y a pas de market_overview
            if 'market_overview' not in data:
                print(style.SUCCESS("✅ CORRECT: No market_overview in response"))
            else:
                print(style.ERROR("❌ WRONG: market_overview found in response"))
                
        else:
            print(style.ERROR(f"❌ POST failed"))
            print(f"   Response: {response.text[:300]}...")
            
    except Exception as e:
        print(style.ERROR(f"❌ POST exception: {e}"))

if __name__ == '__main__':
    test_clean_api()
