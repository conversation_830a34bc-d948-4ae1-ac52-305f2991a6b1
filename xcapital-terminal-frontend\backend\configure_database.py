#!/usr/bin/env python
"""
Script interactif pour configurer la base de données
"""
import os
import sys
from django.core.management.color import make_style

style = make_style()

def create_env_file(db_type):
    """Crée un fichier .env avec la configuration de base de données"""
    env_content = ""
    
    if db_type == "sqlite":
        env_content = """# Configuration SQLite pour développement
USE_POSTGRESQL=False
USE_LOCAL_POSTGRES=False
DATABASE_TYPE=sqlite
"""
    elif db_type == "local_postgres":
        env_content = """# Configuration PostgreSQL local
USE_POSTGRESQL=False
USE_LOCAL_POSTGRES=True
DATABASE_TYPE=local_postgres
"""
    elif db_type == "aws_postgres":
        env_content = """# Configuration PostgreSQL AWS RDS
USE_POSTGRESQL=True
USE_LOCAL_POSTGRES=False
DATABASE_TYPE=aws_postgres
"""
    
    env_path = os.path.join(os.path.dirname(__file__), '.env')
    with open(env_path, 'w') as f:
        f.write(env_content)
    
    print(f"✅ Fichier .env créé: {env_path}")

def show_database_options():
    """Affiche les options de base de données disponibles"""
    print(style.SUCCESS("🗄️  Options de Base de Données Disponibles"))
    print("=" * 60)
    print()
    print("1. 📁 SQLite (Recommandé pour développement)")
    print("   ✅ Aucune installation requise")
    print("   ✅ Fonctionne immédiatement")
    print("   ✅ Parfait pour tester les APIs")
    print("   ⚠️  Limité pour la production")
    print()
    print("2. 🐘 PostgreSQL Local")
    print("   ✅ Performance élevée")
    print("   ✅ Compatible avec la production")
    print("   ❌ Nécessite l'installation de PostgreSQL")
    print("   ❌ Configuration supplémentaire requise")
    print()
    print("3. ☁️  PostgreSQL AWS RDS")
    print("   ✅ Base de données cloud")
    print("   ✅ Haute disponibilité")
    print("   ❌ Nécessite une connexion Internet")
    print("   ❌ L'instance actuelle semble inaccessible")
    print()

def get_user_choice():
    """Demande à l'utilisateur de choisir une option"""
    while True:
        try:
            choice = input("Choisissez une option (1-3): ").strip()
            if choice in ['1', '2', '3']:
                return int(choice)
            else:
                print("❌ Veuillez entrer 1, 2 ou 3")
        except KeyboardInterrupt:
            print("\n👋 Configuration annulée")
            sys.exit(0)

def setup_sqlite():
    """Configuration SQLite"""
    print(style.SUCCESS("📁 Configuration SQLite"))
    print("=" * 40)
    
    create_env_file("sqlite")
    
    print("✅ SQLite configuré avec succès!")
    print()
    print("📋 Prochaines étapes:")
    print("1. python setup_database.py")
    print("2. python manage.py runserver")

def setup_local_postgres():
    """Configuration PostgreSQL local"""
    print(style.SUCCESS("🐘 Configuration PostgreSQL Local"))
    print("=" * 40)
    
    print("📋 Prérequis:")
    print("1. Installer PostgreSQL sur votre machine")
    print("2. Créer une base de données 'xcapital_local'")
    print("3. Créer un utilisateur 'postgres' avec mot de passe 'postgres'")
    print()
    print("💻 Commandes PostgreSQL:")
    print("   createdb xcapital_local")
    print("   psql -c \"ALTER USER postgres PASSWORD 'postgres';\"")
    print()
    
    confirm = input("PostgreSQL est-il installé et configuré? (y/N): ").strip().lower()
    if confirm == 'y':
        create_env_file("local_postgres")
        print("✅ PostgreSQL local configuré!")
        print()
        print("📋 Prochaines étapes:")
        print("1. python setup_database.py")
        print("2. python manage.py runserver")
    else:
        print("⚠️  Veuillez d'abord installer et configurer PostgreSQL")

def setup_aws_postgres():
    """Configuration PostgreSQL AWS"""
    print(style.SUCCESS("☁️  Configuration PostgreSQL AWS RDS"))
    print("=" * 40)
    
    print("⚠️  ATTENTION: L'instance AWS RDS actuelle n'est pas accessible")
    print("   Hostname: xcapitaldatabase.cbeceekg4vdf.eu-north-1.rds.amazonaws.com")
    print()
    print("🔍 Vérifications nécessaires:")
    print("1. L'instance RDS existe-t-elle toujours?")
    print("2. Le hostname est-il correct?")
    print("3. Les groupes de sécurité permettent-ils l'accès?")
    print("4. Votre IP est-elle autorisée?")
    print()
    
    confirm = input("Voulez-vous quand même configurer AWS RDS? (y/N): ").strip().lower()
    if confirm == 'y':
        create_env_file("aws_postgres")
        print("✅ AWS RDS configuré (mais probablement non fonctionnel)")
        print("⚠️  Vous devrez résoudre les problèmes de connectivité")
    else:
        print("👍 Sage décision. Choisissez SQLite ou PostgreSQL local.")

def main():
    """Fonction principale"""
    print(style.SUCCESS("🎯 XCapital Terminal - Configuration de Base de Données"))
    print("=" * 80)
    print()
    
    show_database_options()
    choice = get_user_choice()
    print()
    
    if choice == 1:
        setup_sqlite()
    elif choice == 2:
        setup_local_postgres()
    elif choice == 3:
        setup_aws_postgres()
    
    print()
    print(style.SUCCESS("🎉 Configuration terminée!"))
    print()
    print("📚 Documentation supplémentaire:")
    print("   - SQLite: https://docs.djangoproject.com/en/5.2/ref/databases/#sqlite-notes")
    print("   - PostgreSQL: https://docs.djangoproject.com/en/5.2/ref/databases/#postgresql-notes")

if __name__ == '__main__':
    main()
