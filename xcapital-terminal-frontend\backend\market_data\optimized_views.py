"""
Vues optimisées pour les APIs XCapital avec cache et optimisations de performance
"""
from rest_framework import generics, status, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Sum, Avg, Max, Min, Prefetch
from django.db import models, connection
from django.core.cache import cache, caches
from django.views.decorators.cache import cache_page
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.utils import timezone
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta, date
from decimal import Decimal
import logging
import hashlib

from .postgres_models import XCapitalCompany, XCapitalCompanyBond
from .postgres_serializers import XCapitalCompanyBondSerializer

logger = logging.getLogger(__name__)

class OptimizedPagination(PageNumberPagination):
    """Pagination optimisée avec tailles variables"""
    page_size = 100
    page_size_query_param = 'page_size'
    max_page_size = 1000
    
    def get_paginated_response(self, data):
        return Response({
            'links': {
                'next': self.get_next_link(),
                'previous': self.get_previous_link()
            },
            'count': self.page.paginator.count,
            'page_size': self.page_size,
            'total_pages': self.page.paginator.num_pages,
            'current_page': self.page.number,
            'results': data
        })


def generate_cache_key(prefix, **kwargs):
    """Génère une clé de cache unique basée sur les paramètres"""
    key_data = f"{prefix}:" + ":".join([f"{k}={v}" for k, v in sorted(kwargs.items())])
    return hashlib.md5(key_data.encode()).hexdigest()


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
@csrf_exempt
def optimized_company_price_data(request):
    """
    API optimisée pour récupérer les données de prix avec cache et pagination
    """
    client_ip = request.META.get('HTTP_X_FORWARDED_FOR', request.META.get('REMOTE_ADDR', 'Unknown'))
    logger.info(f"Optimized API Request from IP: {client_ip} - Method: {request.method}")
    
    if request.method == 'GET':
        return Response({
            'success': True,
            'api_info': {
                'name': 'API XCapital Optimisée',
                'version': '2.0',
                'description': 'API haute performance avec cache et pagination',
                'features': [
                    'Cache intelligent multi-niveau',
                    'Pagination optimisée',
                    'Requêtes SQL optimisées',
                    'Limitation automatique des données',
                    'Indexation avancée'
                ]
            },
            'performance_improvements': {
                'cache_layers': ['Memory cache', 'Query cache', 'Result cache'],
                'max_records_per_request': 10000,
                'typical_response_time': '< 500ms',
                'cache_duration': '5-60 minutes selon le type de données'
            }
        })
    
    elif request.method == 'POST':
        try:
            # Validation des données
            company_symbol = request.data.get('company_symbol', '').strip().upper()
            start_date = request.data.get('start_date', '2022-08-22')
            end_date = request.data.get('end_date', timezone.now().date().strftime('%Y-%m-%d'))
            page_size = min(int(request.data.get('page_size', 1000)), 10000)
            use_cache = request.data.get('use_cache', True)
            
            if not company_symbol:
                return Response({
                    'error': 'Symbole d\'entreprise requis',
                    'required_fields': ['company_symbol']
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Génération de la clé de cache
            cache_key = generate_cache_key(
                'company_price_data_v2',
                symbol=company_symbol,
                start=start_date,
                end=end_date,
                page_size=page_size
            )
            
            # Vérifier le cache d'abord
            if use_cache:
                cached_result = cache.get(cache_key)
                if cached_result:
                    logger.info(f"Cache hit for {company_symbol}")
                    cached_result['cache_info'] = {
                        'cache_hit': True,
                        'cache_key': cache_key[:16] + '...',
                        'cached_at': cached_result.get('timestamp')
                    }
                    return Response(cached_result, status=status.HTTP_200_OK)
            
            # Validation des dates
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError as e:
                return Response({
                    'error': 'Format de date invalide',
                    'message': 'Utilisez le format YYYY-MM-DD',
                    'error_detail': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Récupération optimisée de l'entreprise avec cache
            company_cache_key = f"company:{company_symbol}"
            company = cache.get(company_cache_key)
            
            if not company:
                try:
                    company = XCapitalCompany.objects.get(symbol=company_symbol)
                    cache.set(company_cache_key, company, 3600)  # Cache 1 heure
                except XCapitalCompany.DoesNotExist:
                    return Response({
                        'error': f'Entreprise "{company_symbol}" non trouvée'
                    }, status=status.HTTP_404_NOT_FOUND)
            
            # Requête optimisée avec select_related et limitation
            queryset = XCapitalCompanyBond.objects.select_related('company').filter(
                company=company,
                date_trade__gte=start_date_obj,
                date_trade__lte=end_date_obj
            ).order_by('date_trade')
            
            # Limitation de sécurité
            total_count = queryset.count()
            if total_count > 50000:  # Limite de sécurité
                return Response({
                    'error': 'Trop de données demandées',
                    'message': f'La période demandée contient {total_count} enregistrements. Limite: 50,000',
                    'suggestion': 'Réduisez la période ou utilisez la pagination'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Application de la pagination
            price_data = queryset[:page_size]
            
            # Formatage optimisé des données
            formatted_data = []
            for bond in price_data:
                formatted_data.append({
                    'date': bond.date_trade.strftime('%Y-%m-%d'),
                    'open_price': float(bond.open_price) if bond.open_price else None,
                    'high_price': float(bond.high_price) if bond.high_price else None,
                    'low_price': float(bond.low_price) if bond.low_price else None,
                    'close_price': float(bond.close_price) if bond.close_price else None,
                    'volume': bond.volume if bond.volume else 0,
                    'market_cap': float(bond.market_cap) if bond.market_cap else None,
                })
            
            # Calcul des statistiques avec agrégations optimisées
            if price_data:
                stats = queryset.aggregate(
                    max_price=Max('high_price'),
                    min_price=Min('low_price'),
                    avg_volume=Avg('volume'),
                    total_volume=Sum('volume')
                )
                
                first_record = price_data[0] if price_data else None
                last_record = price_data[len(price_data)-1] if price_data else None
                
                first_price = first_record.close_price if first_record and first_record.close_price else Decimal('0')
                last_price = last_record.close_price if last_record and last_record.close_price else Decimal('0')
                
                period_change = last_price - first_price if first_price and last_price else Decimal('0')
                period_change_pct = (period_change / first_price * 100) if first_price else Decimal('0')
            else:
                stats = {'max_price': 0, 'min_price': 0, 'avg_volume': 0, 'total_volume': 0}
                first_price = last_price = period_change = period_change_pct = Decimal('0')
            
            # Préparation de la réponse
            response_data = {
                'success': True,
                'timestamp': timezone.now().isoformat(),
                'company_info': {
                    'symbol': company.symbol,
                    'nom_francais': company.nom_francais,
                    'company_id': company.company_id
                },
                'period': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'total_available_records': total_count,
                    'returned_records': len(formatted_data),
                    'page_size': page_size
                },
                'statistics': {
                    'first_price': float(first_price),
                    'last_price': float(last_price),
                    'period_change': float(period_change),
                    'period_change_pct': float(period_change_pct),
                    'max_price': float(stats['max_price'] or 0),
                    'min_price': float(stats['min_price'] or 0),
                    'avg_volume': float(stats['avg_volume'] or 0),
                    'total_volume': int(stats['total_volume'] or 0)
                },
                'performance_info': {
                    'cache_used': use_cache,
                    'query_optimized': True,
                    'records_limited': total_count > page_size
                },
                'data': formatted_data
            }
            
            # Mise en cache du résultat
            if use_cache and len(formatted_data) > 0:
                cache_timeout = 300 if total_count < 1000 else 600  # 5-10 minutes selon la taille
                cache.set(cache_key, response_data, cache_timeout)
                logger.info(f"Cached result for {company_symbol} ({len(formatted_data)} records)")
            
            return Response(response_data, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Error in optimized_company_price_data: {e}")
            return Response({
                'error': 'Erreur interne du serveur',
                'message': str(e),
                'timestamp': timezone.now().isoformat()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@cache_page(60 * 15)  # Cache pendant 15 minutes
def optimized_market_overview(request):
    """Vue d'ensemble du marché avec cache"""
    cache_key = 'market_overview_v2'
    cached_data = caches['long_term'].get(cache_key)
    
    if cached_data:
        return Response(cached_data)
    
    try:
        # Utilisation d'agrégations optimisées
        company_stats = XCapitalCompany.objects.aggregate(
            total_companies=Count('id')
        )
        
        bond_stats = XCapitalCompanyBond.objects.aggregate(
            total_records=Count('id'),
            latest_date=Max('date_trade'),
            total_volume=Sum('volume')
        )
        
        # Données récentes avec requête optimisée
        recent_date = date.today() - timedelta(days=30)
        recent_companies = XCapitalCompany.objects.filter(
            bonds__date_trade__gte=recent_date
        ).distinct().count()
        
        overview_data = {
            'market_overview': {
                'total_companies': company_stats['total_companies'],
                'total_price_records': bond_stats['total_records'],
                'companies_with_recent_data': recent_companies,
                'latest_trade_date': bond_stats['latest_date'],
                'total_volume': bond_stats['total_volume'] or 0,
                'data_source': 'PostgreSQL Database (Optimized)',
                'cache_info': 'Cached for 15 minutes'
            },
            'timestamp': timezone.now().isoformat()
        }
        
        # Cache pour 15 minutes
        caches['long_term'].set(cache_key, overview_data, 900)
        
        return Response(overview_data)
        
    except Exception as e:
        logger.error(f"Error in optimized_market_overview: {e}")
        return Response({
            'error': 'Erreur lors de la récupération des données',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def clear_cache(request):
    """Endpoint pour vider le cache (admin seulement)"""
    try:
        cache.clear()
        caches['long_term'].clear()
        return Response({
            'success': True,
            'message': 'Cache vidé avec succès',
            'timestamp': timezone.now().isoformat()
        })
    except Exception as e:
        return Response({
            'error': 'Erreur lors du vidage du cache',
            'message': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
